﻿﻿<UserControl x:Class="DriverManagementSystem.Views.AssignmentReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="1000" d:DesignWidth="800"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- تعريف الأنماط -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="ContentTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="TextAlignment" Value="Justify"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="20"/>
        </Style>
        
        <Style x:Key="TableHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Padding" Value="5"/>
        </Style>
        
        <Style x:Key="TableCellStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Arial"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Padding" Value="5"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Border Background="White" Padding="40" MinWidth="750" MinHeight="1000">
            <StackPanel>
                
                <!-- Header Section -->
                <Grid Margin="0,0,0,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Left Side - English Text -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Top">
                        <TextBlock Text="Republic OF YEMEN" FontSize="12" FontWeight="Bold" HorizontalAlignment="Left"/>
                        <TextBlock Text="Social Fund For Development" FontSize="11" FontWeight="Bold" HorizontalAlignment="Left"/>
                        <TextBlock Text="Presidency of Council of Ministers" FontSize="10" FontWeight="Bold" HorizontalAlignment="Left"/>
                        <TextBlock Text="Dhamar Albidaa Branch" FontSize="10" FontWeight="Bold" HorizontalAlignment="Left"/>
                    </StackPanel>
                    
                    <!-- Center - Logo and Arabic Text -->
                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Top">
                        <!-- Logo -->
                        <Border Background="#0066CC" Width="60" Height="60" CornerRadius="8" Margin="0,0,0,10">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="الصندوق" FontSize="10" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="الاجتماعي" FontSize="10" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="للتنمية" FontSize="10" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        
                        <!-- Arabic Header -->
                        <TextBlock Text="الجمهورية اليمنية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="رئاسة مجلس الوزراء" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="فرع ذمار والبيضاء" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </StackPanel>
                    
                    <!-- Right Side - Date -->
                    <StackPanel Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Top">
                        <TextBlock FontSize="11" HorizontalAlignment="Right">
                            <Run Text="الموافق: "/>
                            <Run Text="{Binding AssignmentData.GregorianDate, FallbackValue='2025/06/16'}"/>
                            <Run Text="م"/>
                        </TextBlock>
                        <TextBlock FontSize="11" HorizontalAlignment="Right" Margin="0,5,0,0">
                            <Run Text="التاريخ: "/>
                            <Run Text="{Binding AssignmentData.HijriDate, FallbackValue='20/ذو الحجة/1446'}"/>
                            <Run Text="هـ"/>
                        </TextBlock>
                    </StackPanel>
                </Grid>
                
                <!-- Title Section -->
                <StackPanel Margin="0,20,0,30" HorizontalAlignment="Center">
                    <TextBlock Text="تكليف" FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <Border Height="3" Background="Black" Width="100" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <!-- Main Content -->
                <TextBlock FontSize="16" FontWeight="Bold" TextAlignment="Center" Margin="0,0,0,20" Style="{StaticResource ContentTextStyle}">
                    <Run Text="يكلف الصندوق الاجتماعي للتنمية - فرع ذمار البيضاء المبين اسمائهم في الجدول"/>
                    <LineBreak/>
                    <Run Text="أدناه لتنفيذ المهمة التالية:-"/>
                </TextBlock>
                
                <!-- Project Details -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Style="{StaticResource ContentTextStyle}">
                        <Run Text="• المشروع: "/>
                        <Run Text="{Binding AssignmentData.ProjectName, FallbackValue='الدعم والتطوير المؤسسي لفرع المؤسسة المحلية للمياه والصرف الصحي بمدينة البيضاء محافظة البيضاء - محافظة البيضاء'}"/>
                    </TextBlock>
                    
                    <TextBlock FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Style="{StaticResource ContentTextStyle}">
                        <Run Text="• النشاط: "/>
                        <Run Text="{Binding AssignmentData.ActivityName, FallbackValue='حضور دورة تدريب اللجان المجتمعية لمشروع مياه مدينة البيضاء'}"/>
                    </TextBlock>
                    
                    <TextBlock FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Style="{StaticResource ContentTextStyle}">
                        <Run Text="• خط السير: "/>
                        <Run Text="{Binding AssignmentData.Route, FallbackValue='ذمار-رداع البيضاء'}"/>
                    </TextBlock>
                    
                    <TextBlock FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Style="{StaticResource ContentTextStyle}">
                        <Run Text="• تاريخ التحرك: من "/>
                        <Run Text="{Binding AssignmentData.StartDate, FallbackValue='27/06/2025'}"/>
                        <Run Text=" الى "/>
                        <Run Text="{Binding AssignmentData.EndDate, FallbackValue='29/06/2025'}"/>
                    </TextBlock>
                </StackPanel>
                
            </StackPanel>
        </Border>
    </ScrollViewer>
</UserControl>

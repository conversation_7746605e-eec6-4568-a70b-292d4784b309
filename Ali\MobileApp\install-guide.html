<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تثبيت التطبيق</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            margin: 0;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }
        h1 {
            color: #6366f1;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        h2 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .step {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            border-right: 4px solid #6366f1;
        }
        .step-number {
            background: #6366f1;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        .platform {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            font-weight: 600;
        }
        .note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #92400e;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #065f46;
        }
        .code {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        .qr-placeholder {
            width: 200px;
            height: 200px;
            background: #f3f4f6;
            border: 2px dashed #9ca3af;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #6b7280;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>📱 دليل تثبيت تطبيق إدارة المشاريع</h1>
        
        <div class="success">
            <strong>🎉 تهانينا!</strong> تطبيقك جاهز للتثبيت على الهاتف
        </div>

        <h2>🌐 للأندرويد (Chrome/Edge)</h2>
        <div class="platform">أندرويد - متصفح Chrome أو Edge</div>
        
        <div class="step">
            <span class="step-number">1</span>
            افتح المتصفح واذهب إلى رابط التطبيق
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            ستظهر رسالة "إضافة إلى الشاشة الرئيسية" - اضغط عليها
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            اضغط "تثبيت" أو "Install" لتثبيت التطبيق
        </div>

        <h2>🍎 للآيفون (Safari)</h2>
        <div class="platform">آيفون - متصفح Safari</div>
        
        <div class="step">
            <span class="step-number">1</span>
            افتح Safari واذهب إلى رابط التطبيق
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            اضغط على زر المشاركة <i class="fas fa-share"></i> في الأسفل
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            اختر "إضافة إلى الشاشة الرئيسية" من القائمة
        </div>
        
        <div class="step">
            <span class="step-number">4</span>
            اضغط "إضافة" لتثبيت التطبيق
        </div>

        <h2>💻 رفع التطبيق على الخادم</h2>
        
        <div class="note">
            <strong>ملاحظة:</strong> لتشغيل التطبيق على الهاتف، يجب رفعه على خادم ويب
        </div>
        
        <div class="step">
            <span class="step-number">1</span>
            ارفع جميع ملفات المجلد على استضافة ويب
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            تأكد من أن الملفات التالية موجودة:
            <div class="code">
index.html
mobile-styles.css
mobile-app.js
manifest.json
sw.js
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            افتح الرابط في متصفح الهاتف واتبع خطوات التثبيت أعلاه
        </div>

        <h2>🔧 خيارات أخرى</h2>
        
        <div class="step">
            <strong>خادم محلي:</strong> يمكنك استخدام XAMPP أو WAMP لتشغيل التطبيق محلياً
        </div>
        
        <div class="step">
            <strong>GitHub Pages:</strong> ارفع الملفات على GitHub واستخدم GitHub Pages مجاناً
        </div>
        
        <div class="step">
            <strong>Netlify/Vercel:</strong> خدمات استضافة مجانية سهلة الاستخدام
        </div>

        <div class="qr-code">
            <h3>📱 رمز QR للوصول السريع</h3>
            <div class="qr-placeholder">
                <i class="fas fa-qrcode"></i>
            </div>
            <p>امسح الرمز بكاميرا الهاتف للوصول المباشر</p>
        </div>

        <div class="success">
            <strong>✅ بعد التثبيت:</strong> ستجد أيقونة التطبيق على الشاشة الرئيسية لهاتفك وسيعمل مثل التطبيقات العادية!
        </div>
    </div>
</body>
</html>

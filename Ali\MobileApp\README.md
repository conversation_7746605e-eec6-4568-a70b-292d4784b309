# 📱 تطبيق إدارة المشاريع الهندسية

تطبيق ويب تقدمي (PWA) لإدارة المشاريع الهندسية مع إمكانية التثبيت على الهواتف الذكية.

## 🌟 المميزات

- **💼 اذونات ضبط** - إدارة أذونات الصب والخرسانة
- **📋 استلام اعمال** - متابعة أعمال الإنشاء والتشطيب  
- **✅ اعتماد عينات** - فحص واعتماد العينات
- **🧪 اختبارات** - إجراء الاختبارات المطلوبة
- **👤 تصريح عمل** - إدارة تصاريح العمل
- **📄 إدارة مذكرة** - متابعة المذكرات والوثائق

## 📱 التثبيت على الهاتف

### للأندرويد (Chrome/Edge):
1. افتح المتصفح واذهب إلى رابط التطبيق
2. ستظهر رسالة "إضافة إلى الشاشة الرئيسية" - اضغط عليها
3. اضغط "تثبيت" لتثبيت التطبيق

### للآيفون (Safari):
1. افتح Safari واذهب إلى رابط التطبيق
2. اضغط على زر المشاركة 📤 في الأسفل
3. اختر "إضافة إلى الشاشة الرئيسية"
4. اضغط "إضافة" لتثبيت التطبيق

## 🚀 التشغيل

### الطريقة الأولى - خادم محلي:
```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx serve .

# باستخدام PHP
php -S localhost:8000
```

### الطريقة الثانية - رفع على استضافة:
1. ارفع جميع الملفات على استضافة ويب
2. تأكد من وجود HTTPS للـ PWA
3. افتح الرابط في متصفح الهاتف

### الطريقة الثالثة - GitHub Pages:
1. ارفع الملفات على مستودع GitHub
2. فعل GitHub Pages من الإعدادات
3. استخدم الرابط المُنشأ

## 📁 هيكل الملفات

```
MobileApp/
├── index.html              # الصفحة الرئيسية
├── mobile-styles.css       # ملف التصميم
├── mobile-app.js          # ملف JavaScript
├── manifest.json          # إعدادات PWA
├── sw.js                  # Service Worker
├── install-guide.html     # دليل التثبيت
├── create-icons.html      # مولد الأيقونات
└── README.md             # هذا الملف
```

## 🎨 إنشاء الأيقونات

1. افتح `create-icons.html` في المتصفح
2. اضغط "إنشاء وتحميل الأيقونات"
3. حمل جميع الأحجام المطلوبة
4. ضع الأيقونات في مجلد التطبيق

## 🔧 التخصيص

### تغيير الألوان:
```css
/* في mobile-styles.css */
:root {
  --primary-color: #6366f1;
  --secondary-color: #8b5cf6;
  --background-color: #f8fafc;
}
```

### إضافة صفحات جديدة:
1. أضف HTML للصفحة في `index.html`
2. أضف CSS في `mobile-styles.css`
3. أضف JavaScript في `mobile-app.js`

## 📊 المتطلبات

- متصفح حديث يدعم PWA
- HTTPS للتثبيت (في الإنتاج)
- JavaScript مُفعل

## 🛠️ التطوير

### إضافة مميزات جديدة:
1. أضف العنصر في القائمة الرئيسية
2. أنشئ الصفحة المطلوبة
3. أضف التصميم والتفاعل
4. اختبر على الهاتف

### تحديث Service Worker:
```javascript
// في sw.js - غير رقم الإصدار
const CACHE_NAME = 'engineering-projects-v2';
```

## 🔒 الأمان

- جميع البيانات محلية (لا ترسل للخادم)
- لا يتطلب أذونات خاصة
- يعمل بدون اتصال بالإنترنت

## 📞 الدعم

للمساعدة أو الاستفسارات:
- افتح `install-guide.html` للتعليمات المفصلة
- تأكد من تحديث المتصفح
- تأكد من وجود اتصال إنترنت للتثبيت الأول

## 📝 الترخيص

هذا التطبيق مجاني للاستخدام الشخصي والتجاري.

---

**🎉 استمتع باستخدام تطبيق إدارة المشاريع على هاتفك!**

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات التطبيق</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8fafc;
            padding: 20px;
            text-align: center;
        }
        .icon-generator {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        .icon-preview {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            border-radius: 40px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 80px;
            box-shadow: 0 8px 30px rgba(99, 102, 241, 0.3);
        }
        .download-btn {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            font-family: 'Cairo', sans-serif;
        }
        .size-info {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-size: 14px;
            color: #64748b;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="icon-generator">
        <h1>🎨 مولد أيقونات التطبيق</h1>
        <p>أيقونة تطبيق إدارة المشاريع الهندسية</p>
        
        <div class="icon-preview" id="iconPreview">
            <i class="fas fa-hard-hat"></i>
        </div>
        
        <div class="size-info">
            سيتم إنشاء جميع الأحجام المطلوبة للتطبيق
        </div>
        
        <button class="download-btn" onclick="generateIcons()">
            <i class="fas fa-download"></i> إنشاء وتحميل الأيقونات
        </button>
        
        <div id="downloadLinks"></div>
    </div>

    <script>
        function generateIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const downloadLinks = document.getElementById('downloadLinks');
            
            downloadLinks.innerHTML = '<h3>تحميل الأيقونات:</h3>';
            
            sizes.forEach(size => {
                canvas.width = size;
                canvas.height = size;
                
                // Background gradient
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#6366f1');
                gradient.addColorStop(1, '#8b5cf6');
                
                // Draw background
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);
                
                // Draw icon (simplified helmet shape)
                ctx.fillStyle = 'white';
                ctx.font = `${size * 0.4}px FontAwesome`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('⛑️', size/2, size/2);
                
                // Create download link
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `icon-${size}x${size}.png`;
                    link.textContent = `تحميل ${size}x${size}`;
                    link.className = 'download-btn';
                    link.style.display = 'inline-block';
                    link.style.margin = '5px';
                    downloadLinks.appendChild(link);
                });
            });
        }
    </script>
</body>
</html>
